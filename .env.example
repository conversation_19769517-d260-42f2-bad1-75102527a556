# OpenAI API Key
OPENAI_API_KEY=your-openai-api-key-here
LLM_MODEL=gpt-4o-mini


# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9094
# KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_AGENT_CREATION_TOPIC=agent_creation_requests
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_CONSUMER_GROUP=autogen-agent-service-group
KAFKA_AGENT_QUERY_TOPIC=agent_query_requests
KAFKA_AGENT_MESSAGE_TOPIC=agent_message_requests
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests
KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC=orchestration_team_session_requests
KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC=orchestration_team_chat_requests
KAFKA_HUMAN_INPUT_REQUEST_TOPIC=human_input_requests
KAFKA_HUMAN_INPUT_RESPONSE_TOPIC=human_input_responses

# Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# API Gateway
API_GATEWAY_URL=https://app-dev.rapidinnovation.dev/api/v1
API_GATEWAY_KEY=your_api_gateway_key_here

# Workflow API Gateway
WORKFLOW_API_GATEWAY_URL=https://ruh-execution.rapidinnovation.dev/api/v1
WORKFLOW_API_GATEWAY_KEY=your_workflow_api_key_here

# Pinecone Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=agent-memory
PINECONE_DIMENSION=1536
PINECONE_METRIC=cosine
PINECONE_CLOUD=aws


EXA_API_KEY=your_exa_api_key_here