import logging

# Import settings directly without relative imports
import os
import sys
from typing import Any, Dict, Optional

from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.messages import TextMessage
from autogen_core.model_context import BufferedChatCompletionContext

from ...model_factory import ModelFactory

sys.path.append(os.path.join(os.path.dirname(__file__), "../../.."))
from app.shared.config.base import get_settings

logger = logging.getLogger(__name__)


class QueryAnalysisAgent:
    """
    Agent responsible for analyzing user queries and extracting task requirements
    in a structured format for the discovery pipeline.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Query Analysis Agent.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv(
            "DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05"
        )
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "google")
        self.agent = None

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            self.logger.info("Initializing Query Analysis Agent...")

            # Create model client using ModelFactory
            model_config = {
                "llm_type": self.llm_type,
                "provider": (
                    "GoogleChatCompletionClient"
                    if self.llm_type == "google"
                    else "OpenAIChatCompletionClient"
                ),
                "model": self.model_name,
                "api_key": self.api_key,
                "base_url": (
                    self.settings.requesty.base_url
                    if self.llm_type == "google"
                    else None
                ),
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError(f"Failed to create {self.llm_type} model client")

            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Define system message for query analysis
            system_message = """You are a Query Analysis Agent specialized in analyzing user queries to extract key information for agent routing.

Your task is to analyze user queries and extract:
1. Domain/category of the query
2. Task summary in one clear sentence
3. Technical requirements needed
4. Keywords for agent matching
5. Urgency level (low, medium, high)
6. Complexity level (simple, moderate, complex)

Always respond in a structured format with clear categorization. Focus on understanding the user's intent and technical needs."""

            # Create the assistant agent
            self.agent = AssistantAgent(
                name="query_analysis_agent",
                description="Analyzes user queries to extract requirements and categorize tasks",
                model_client=chat_completion_client,
                system_message=system_message,
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info("Query Analysis Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Query Analysis Agent: {e}")
            raise e

    async def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze a user query and extract structured task requirements.

        Args:
            query: User query to analyze

        Returns:
            Dict containing analysis results
        """
        if not self.agent:
            raise ValueError(
                "Query Analysis Agent not initialized. Call initialize() first."
            )

        try:
            self.logger.info(f"Analyzing query: {query[:100]}...")

            # Create analysis prompt
            analysis_prompt = f"""
            Please analyze this user query and extract structured task requirements:
            
            USER QUERY: "{query}"
            
            Provide your analysis in exactly this format:
            
            DOMAIN: [main domain/category]
            TASK_SUMMARY: [one clear sentence describing the task]
            REQUIREMENTS: [requirement1, requirement2, requirement3]
            CONSTRAINTS: [constraint1, constraint2]
            TECHNOLOGIES: [tech1, tech2, tech3]
            OUTPUT_TYPE: [expected output type]
            URGENCY: [low/medium/high]
            COMPLEXITY: [simple/moderate/complex]
            KEYWORDS: [keyword1, keyword2, keyword3]
            """

            text_message = TextMessage(content=analysis_prompt, source="user")
            response = await self.agent.on_messages(
                [text_message], cancellation_token=None
            )

            # Extract response content
            if hasattr(response, "chat_message") and hasattr(
                response.chat_message, "content"
            ):
                analysis_result = response.chat_message.content
            elif hasattr(response, "content"):
                analysis_result = response.content
            else:
                analysis_result = str(response)

            # Debug logging to see what we actually got
            self.logger.info(f"Raw analysis result from model: {analysis_result}")

            # Parse the structured response
            parsed_analysis = self._parse_analysis_result(analysis_result)
            parsed_analysis["original_query"] = query
            parsed_analysis["raw_analysis"] = analysis_result

            self.logger.info(
                f"Query analysis completed for domain: {parsed_analysis.get('domain', 'unknown')}"
            )
            return parsed_analysis

        except Exception as e:
            self.logger.error(f"Error analyzing query: {e}")
            return {
                "original_query": query,
                "error": str(e),
                "task_summary": f"Error analyzing query: {str(e)}",
                "domain": "unknown",
                "requirements": [],
                "constraints": [],
                "technologies": [],
                "output_type": "unknown",
                "urgency": "medium",
                "complexity": "unknown",
                "keywords": [],
                "raw_analysis": "",
            }

    def _parse_analysis_result(self, analysis_result: str) -> Dict[str, Any]:
        """Parse the structured analysis result into a dictionary."""
        result = {
            "task_summary": "",
            "domain": "",
            "requirements": [],
            "constraints": [],
            "technologies": [],
            "output_type": "",
            "urgency": "medium",
            "complexity": "moderate",
            "keywords": [],
        }

        try:
            lines = analysis_result.split("\n")
            for line in lines:
                line = line.strip()
                if ":" in line:
                    key, value = line.split(":", 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if key in ["task_summary", "task summary"]:
                        result["task_summary"] = value
                    elif key == "domain":
                        result["domain"] = value
                    elif key == "requirements":
                        result["requirements"] = [
                            req.strip() for req in value.split(",") if req.strip()
                        ]
                    elif key == "constraints":
                        result["constraints"] = [
                            cons.strip() for cons in value.split(",") if cons.strip()
                        ]
                    elif key == "technologies":
                        result["technologies"] = [
                            tech.strip() for tech in value.split(",") if tech.strip()
                        ]
                    elif key in ["output_type", "output type"]:
                        result["output_type"] = value
                    elif key == "urgency":
                        result["urgency"] = value.lower()
                    elif key == "complexity":
                        result["complexity"] = value.lower()
                    elif key == "keywords":
                        result["keywords"] = [
                            kw.strip() for kw in value.split(",") if kw.strip()
                        ]

        except Exception as e:
            self.logger.error(f"Error parsing analysis result: {e}")

        return result

    def get_agent(self) -> AssistantAgent:
        """Get the underlying AssistantAgent instance for group chat."""
        if not self.agent:
            raise ValueError("Agent not initialized")
        return self.agent

    def is_initialized(self) -> bool:
        """Check if the agent is initialized."""
        return self.agent is not None

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
    ) -> "QueryAnalysisAgent":
        """
        Convenience method to create and initialize an agent in one call.
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize()
        return agent
