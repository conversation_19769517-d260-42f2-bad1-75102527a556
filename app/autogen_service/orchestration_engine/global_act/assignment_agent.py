import logging

# Import settings directly without relative imports
import os
import sys
from typing import Any, Dict, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core.model_context import BufferedChatCompletionContext

from ...model_factory import ModelFactory

sys.path.append(os.path.join(os.path.dirname(__file__), "../../.."))
from app.shared.config.base import get_settings

logger = logging.getLogger(__name__)


class AssignmentAgent:
    """
    Agent responsible for formulating the final task assignment with clear
    requirements and context for the selected specialized agent.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Assignment Agent.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv(
            "DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05"
        )
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "google")
        self.agent = None
        self._specialized_agents_cache = {}

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            self.logger.info("Initializing Assignment Agent...")

            # Create model client using ModelFactory
            model_config = {
                "llm_type": self.llm_type,
                "provider": (
                    "GoogleChatCompletionClient"
                    if self.llm_type == "google"
                    else "OpenAIChatCompletionClient"
                ),
                "model": self.model_name,
                "api_key": self.api_key,
                "base_url": (
                    self.settings.requesty.base_url
                    if self.llm_type == "google"
                    else None
                ),
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError(f"Failed to create {self.llm_type} model client")

            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Define system message for assignment
            system_message = """You are an Assignment Agent specialized in routing tasks to selected specialized agents.

Your task is to:
1. Receive the selected agent information from the Selection Agent
2. Create appropriate task assignments for the specialized agent
3. Route the original query to the selected agent
4. Handle the response and format it appropriately for the user

You are the final step in the routing pipeline, ensuring tasks are properly assigned and executed."""

            # Create the assistant agent
            self.agent = AssistantAgent(
                name="assignment_agent",
                description="Routes tasks to selected specialized agents and manages execution",
                model_client=chat_completion_client,
                system_message=system_message,
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info("Assignment Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Assignment Agent: {e}")
            raise e

    async def create_assignment(
        self, selection_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a detailed task assignment based on selection results.

        Args:
            selection_results: Results from SelectionAgent containing selected agent

        Returns:
            Dict containing assignment details
        """
        if not self.agent:
            raise ValueError(
                "Assignment Agent not initialized. Call initialize() first."
            )

        try:
            selected_agent = selection_results.get("selected_agent")
            discovery_results = selection_results.get("discovery_results", {})
            task_analysis = discovery_results.get("task_analysis", {})

            if not selected_agent:
                return {
                    "assignment_created": False,
                    "reason": "No agent was selected for assignment",
                    "recommendation": "Review selection criteria or task requirements",
                    "selected_agent": None,
                    "assignment_details": {},
                    "selection_results": selection_results,
                    "raw_assignment": "",
                }

            self.logger.info(f"Creating assignment for {selected_agent['agent_name']}")

            # Create assignment prompt
            assignment_prompt = f"""
            Please create a detailed task assignment for the selected agent:
            
            ORIGINAL QUERY: {task_analysis.get('original_query', 'No query provided')}
            
            TASK ANALYSIS:
            - Task Summary: {task_analysis.get('task_summary', 'No summary')}
            - Domain: {task_analysis.get('domain', 'unknown')}
            - Requirements: {', '.join(task_analysis.get('requirements', []))}
            - Constraints: {', '.join(task_analysis.get('constraints', []))}
            - Technologies: {', '.join(task_analysis.get('technologies', []))}
            - Output Type: {task_analysis.get('output_type', 'unknown')}
            - Urgency: {task_analysis.get('urgency', 'medium')}
            - Complexity: {task_analysis.get('complexity', 'moderate')}
            
            SELECTED AGENT:
            - Name: {selected_agent['agent_name']}
            - ID: {selected_agent['agent_id']}
            - Specialization: {selected_agent['specialization']}
            - Score: {selected_agent['score']}
            - Available Tools: {', '.join(selected_agent['tools'])}
            - Available Workflows: {', '.join(selected_agent['workflows'])}
            - Expertise Areas: {', '.join(selected_agent['expertise'])}
            - Selection Reason: {selected_agent['reason']}
            
            SELECTION DETAILS:
            - Confidence: {selection_results.get('confidence', 0.0)}
            - Primary Reason: {selection_results.get('reason', 'No reason')}
            - Risk Assessment: {selection_results.get('risk_assessment', 'No assessment')}
            - Recommendation: {selection_results.get('recommendation', 'No recommendation')}
            
            Create a comprehensive task assignment that maximizes the selected agent's capabilities.
            """

            text_message = TextMessage(content=assignment_prompt, source="user")
            response = await self.agent.on_messages(
                [text_message], cancellation_token=None
            )

            # Extract response content
            if hasattr(response, "chat_message") and hasattr(
                response.chat_message, "content"
            ):
                assignment_result = response.chat_message.content
            elif hasattr(response, "content"):
                assignment_result = response.content
            else:
                assignment_result = str(response)

            # Parse the assignment results
            parsed_results = self._parse_assignment_result(assignment_result)
            parsed_results.update(
                {
                    "assignment_created": True,
                    "selected_agent": selected_agent,
                    "selection_results": selection_results,
                    "raw_assignment": assignment_result,
                }
            )

            self.logger.info(
                f"Assignment created successfully for {selected_agent['agent_name']}"
            )
            return parsed_results

        except Exception as e:
            self.logger.error(f"Error creating assignment: {e}")
            return {
                "assignment_created": False,
                "reason": f"Error during assignment creation: {str(e)}",
                "recommendation": "Retry assignment creation or review inputs",
                "selected_agent": selection_results.get("selected_agent"),
                "assignment_details": {},
                "selection_results": selection_results,
                "error": str(e),
                "raw_assignment": "",
            }

    def _parse_assignment_result(self, assignment_result: str) -> Dict[str, Any]:
        """Parse the structured assignment result into a dictionary."""
        result = {
            "assignment_details": {
                "assigned_to": "",
                "priority": "medium",
                "context": "",
                "objective": "",
                "requirements": [],
                "constraints": [],
                "recommended_tools": [],
                "recommended_workflows": [],
                "deliverables": [],
                "success_criteria": [],
                "additional_notes": "",
            }
        }

        try:
            lines = assignment_result.split("\n")
            current_key = None

            for line in lines:
                line = line.strip()

                if ":" in line and line.split(":")[0].strip().upper() in [
                    "ASSIGNED_TO",
                    "PRIORITY",
                    "CONTEXT",
                    "OBJECTIVE",
                    "REQUIREMENTS",
                    "CONSTRAINTS",
                    "RECOMMENDED_TOOLS",
                    "RECOMMENDED_WORKFLOWS",
                    "DELIVERABLES",
                    "SUCCESS_CRITERIA",
                    "ADDITIONAL_NOTES",
                ]:
                    key, value = line.split(":", 1)
                    current_key = key.strip().lower()
                    value = value.strip()

                    if current_key in [
                        "assigned_to",
                        "priority",
                        "context",
                        "objective",
                        "additional_notes",
                    ]:
                        result["assignment_details"][current_key] = value
                    elif current_key in [
                        "requirements",
                        "constraints",
                        "deliverables",
                        "success_criteria",
                    ]:
                        if value:
                            result["assignment_details"][current_key] = [
                                item.strip()
                                for item in value.split(",")
                                if item.strip()
                            ]
                    elif current_key in ["recommended_tools", "recommended_workflows"]:
                        if value:
                            result["assignment_details"][current_key] = [
                                item.strip()
                                for item in value.split(",")
                                if item.strip()
                            ]
                elif current_key and line and not line.startswith("-"):
                    # Continue multi-line values
                    if current_key in ["context", "objective", "additional_notes"]:
                        if result["assignment_details"][current_key]:
                            result["assignment_details"][current_key] += " " + line
                        else:
                            result["assignment_details"][current_key] = line
                elif line.startswith("-") and current_key in [
                    "requirements",
                    "constraints",
                    "deliverables",
                    "success_criteria",
                    "recommended_tools",
                    "recommended_workflows",
                ]:
                    # Handle bullet points
                    item = line[1:].strip()
                    if item:
                        result["assignment_details"][current_key].append(item)

        except Exception as e:
            self.logger.error(f"Error parsing assignment result: {e}")

        return result

    def format_assignment_for_execution(
        self, assignment_results: Dict[str, Any]
    ) -> str:
        """
        Format the assignment into a clear, executable message for the specialized agent.

        Args:
            assignment_results: Results from create_assignment

        Returns:
            Formatted assignment message
        """
        if not assignment_results.get("assignment_created"):
            return f"Assignment could not be created: {assignment_results.get('reason', 'Unknown error')}"

        details = assignment_results.get("assignment_details", {})
        selected_agent = assignment_results.get("selected_agent", {})

        assignment_message = f"""
As a {selected_agent.get('agent_name', 'Specialized Agent')}, please help with this task:

CONTEXT:
{details.get('context', 'No context provided')}

OBJECTIVE:
{details.get('objective', 'No objective specified')}

REQUIREMENTS:
{chr(10).join(f"- {req}" for req in details.get('requirements', ['No specific requirements']))}

CONSTRAINTS:
{chr(10).join(f"- {const}" for const in details.get('constraints', ['No specific constraints']))}

RECOMMENDED TOOLS:
{', '.join(details.get('recommended_tools', ['Standard tools']))}

RECOMMENDED WORKFLOWS:
{', '.join(details.get('recommended_workflows', ['Standard workflow']))}

EXPECTED DELIVERABLES:
{chr(10).join(f"- {deliv}" for deliv in details.get('deliverables', ['Provide appropriate response']))}

SUCCESS CRITERIA:
{chr(10).join(f"- {crit}" for crit in details.get('success_criteria', ['Task completion']))}

ADDITIONAL NOTES:
{details.get('additional_notes', 'Use your expertise and available tools to provide the best possible assistance.')}

Please use your expertise in {selected_agent.get('specialization', 'your field')} to provide a comprehensive response.
        """.strip()

        return assignment_message

    def get_agent(self) -> AssistantAgent:
        """Get the underlying AssistantAgent instance for group chat."""
        if not self.agent:
            raise ValueError("Agent not initialized")
        return self.agent

    def is_initialized(self) -> bool:
        """Check if the agent is initialized."""
        return self.agent is not None

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
    ) -> "AssignmentAgent":
        """
        Convenience method to create and initialize an agent in one call.
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize()
        return agent

    async def _create_specialized_agent(self, agent_config: Dict) -> AssistantAgent:
        """Create a specialized agent instance from configuration."""
        try:
            # Create model client for the specialized agent
            model_config = {
                "llm_type": self.llm_type,
                "provider": (
                    "GoogleChatCompletionClient"
                    if self.llm_type == "google"
                    else "OpenAIChatCompletionClient"
                ),
                "model": agent_config.get("model_name", self.model_name),
                "api_key": self.api_key,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError("Failed to create model client for specialized agent")

            # Create fresh model context for the specialized agent
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Create the specialized agent
            specialized_agent = AssistantAgent(
                name=agent_config["id"],
                description=agent_config["description"],
                model_client=chat_completion_client,
                system_message=agent_config["system_message"],
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info(
                f"Created specialized agent: {agent_config['name']} (ID: {agent_config['id']})"
            )
            return specialized_agent

        except Exception as e:
            self.logger.error(
                f"Failed to create specialized agent {agent_config['name']}: {e}"
            )
            raise e
